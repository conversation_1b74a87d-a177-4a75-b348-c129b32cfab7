# WebLibya - نظام إدارة المخازن والمبيعات

تطبيق سطح مكتب متكامل لإدارة المخازن، المبيعات، الموردين، والديون، مع نظام أمان وتحكم بالصلاحيات، وواجهة احترافية وحديثة.

## الميزات الرئيسية:
- واجهة رسومية عصرية باستخدام `customtkinter`.
- قاعدة بيانات محلية `SQLite3` مع دعم النسخ الاحتياطي والاستعادة.
- نظام مستخدمين وأمان متقدم مع تشفير كلمات المرور وسجل الدخول/الخروج.
- إدارة شاملة للمخازن تتضمن تتبع الكميات، تنبيهات الحد الأدنى، وتواريخ انتهاء الصلاحية.
- إدارة المبيعات مع إنشاء الفواتير، حساب الخصومات والضرائب، ودعم طرق دفع متعددة.
- إدارة الموردين وتتبع ديونهم.
- نظام جرد وتحليل مع تقارير قابلة للتصدير (PDF/Excel).
- إدارة الديون للعملاء مع تتبع الدفعات والتقارير.
- نظام ترخيص للبرنامج بكلمة مرور رئيسية وتاريخ انتهاء صلاحية.

## هيكل المشروع:
```
weblibya/
├─ main.py
├─ gui/
│  ├─ login.py
│  ├─ dashboard.py
│  ├─ inventory.py
│  ├─ sales.py
│  ├─ suppliers.py
│  ├─ debts.py
│  ├─ settings.py
│  └─ reports.py
├─ db/
│  ├─ weblibya.db
│  └─ db_manager.py
├─ core/
│  ├─ auth.py
│  ├─ license.py
│  └─ backup.py
├─ assets/
│  ├─ images/
│  └─ icons/
├─ utils/
│  ├─ helpers.py
│  └─ validators.py
├─ reports/
│  └─ exported/
├─ requirements.txt
└─ README.md
```

## التثبيت:
للبدء، قم بتثبيت المكتبات المطلوبة باستخدام `pip`:
```bash
pip install -r requirements.txt
```

## التشغيل:
بعد التثبيت، يمكنك تشغيل التطبيق من خلال ملف `main.py`:
```bash
python main.py
```

## المبرمج:
حمزة عبدالكريم التوينسي

## الشركة المنفذة:
WebLibya