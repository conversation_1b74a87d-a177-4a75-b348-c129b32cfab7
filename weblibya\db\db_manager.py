import sqlite3
import logging
from ..utils.config import DATABASE_NAME

class DBManager:
    def __init__(self, db_name=DATABASE_NAME):
        self.db_name = db_name
        self.conn = None
        self.cursor = None

    def connect(self):
        try:
            self.conn = sqlite3.connect(self.db_name)
            self.cursor = self.conn.cursor()
            logging.info(f"Connected to database: {self.db_name}")
        except sqlite3.Error as e:
            logging.error(f"Error connecting to database: {e}")
            raise # Re-raise the exception after logging

    def disconnect(self):
        if self.conn:
            self.conn.close()
            logging.info("Disconnected from database.")

    def execute_query(self, query, params=()):
        try:
            self.cursor.execute(query, params)
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            logging.error(f"Error executing query: {e}", exc_info=True)
            return False

    def fetch_all(self, query, params=()):
        self.cursor.execute(query, params)
        return self.cursor.fetchall()

    def fetch_one(self, query, params=()):
        self.cursor.execute(query, params)
        return self.cursor.fetchone()

    def create_tables(self):
        # Example table creation (users table)
        users_table_query = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'user'
        );
        """
        if self.execute_query(users_table_query):
            logging.info("Table 'users' created or already exists.")

        # Add more table creation queries here as needed for inventory, sales, etc.

if __name__ == '__main__':
    db = DBManager()
    db.connect()
    db.create_tables()
    db.disconnect()