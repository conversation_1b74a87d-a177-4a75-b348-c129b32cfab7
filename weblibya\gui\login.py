import customtkinter
from core.auth import AuthManager

class LoginFrame(customtkinter.CTkFrame):
    def __init__(self, master, **kwargs):
        super().__init__(master, **kwargs)

        self.auth_manager = AuthManager() # Initialize AuthManager

        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure((0, 1, 2, 3, 4), weight=1)

        self.label = customtkinter.CTkLabel(self, text="تسجيل الدخول", font=customtkinter.CTkFont(size=24, weight="bold"))
        self.label.grid(row=0, column=0, padx=20, pady=20, sticky="n")

        self.username_entry = customtkinter.CTkEntry(self, placeholder_text="اسم المستخدم")
        self.username_entry.grid(row=1, column=0, padx=20, pady=10, sticky="ew")

        self.password_entry = customtkinter.CTkEntry(self, placeholder_text="كلمة المرور", show="*")
        self.password_entry.grid(row=2, column=0, padx=20, pady=10, sticky="ew")

        self.login_button = customtkinter.CTkButton(self, text="دخول", command=self.perform_login)
        self.login_button.grid(row=3, column=0, padx=20, pady=20, sticky="ew")

        self.message_label = customtkinter.CTkLabel(self, text="", text_color="red")
        self.message_label.grid(row=4, column=0, padx=20, pady=5, sticky="n")

    def perform_login(self):
        username = self.username_entry.get()
        password = self.password_entry.get()

        user = self.auth_manager.authenticate_user(username, password)

        if user:
            self.message_label.configure(text="تم تسجيل الدخول بنجاح!", text_color="green")
            # Here you would typically switch to the main application view
            print("Login successful!")
        else:
            self.message_label.configure(text="اسم المستخدم أو كلمة المرور غير صحيحة.", text_color="red")
            print("Login failed.")