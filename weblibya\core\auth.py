import logging
from ..db.db_manager import DBManager

class AuthManager:
    def __init__(self, db_manager=None):
        self.db_manager = db_manager if db_manager else DBManager()

    def authenticate_user(self, username, password):
        try:
            self.db_manager.connect()
            user = self.db_manager.fetch_one("SELECT * FROM users WHERE username = ? AND password = ?", (username, password))
            if user:
                logging.info(f"User '{username}' authenticated successfully.")
            else:
                logging.warning(f"Authentication failed for user '{username}'. Invalid credentials.")
            return user
        except Exception as e:
            logging.error(f"Error during authentication for user '{username}': {e}", exc_info=True)
            return None
        finally:
            self.db_manager.disconnect()

def create_default_admin():
    db = DBManager()
    try:
        db.connect()
        db.create_tables() # Ensure tables are created
        # Check if admin user exists
        admin_exists = db.fetch_one("SELECT * FROM users WHERE username = ?", ("admin",))
        if not admin_exists:
            if db.execute_query("INSERT INTO users (username, password, role) VALUES (?, ?, ?)", ("admin", "admin", "admin")):
                logging.info("Default admin user created.")
            else:
                logging.error("Failed to create default admin user.")
        else:
            logging.info("Default admin user already exists.")
    except Exception as e:
        logging.error(f"Error creating default admin user: {e}", exc_info=True)
    finally:
        db.disconnect()