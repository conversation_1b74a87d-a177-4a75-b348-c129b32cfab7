import unittest
import os
from ..db.db_manager import DBManager
from ..utils.config import DATABASE_NAME

class TestDBManager(unittest.TestCase):
    def setUp(self):
        # Use a test-specific database
        self.test_db_name = "test_weblibya.db"
        self.db_manager = DBManager(db_name=self.test_db_name)
        self.db_manager.connect()
        self.db_manager.create_tables()

    def tearDown(self):
        # Clean up the test database
        self.db_manager.disconnect()
        if self.db_manager.conn:
            self.db_manager.conn.close()
        if os.path.exists(self.test_db_name):
            os.remove(self.test_db_name)

    def test_connection(self):
        self.assertIsNotNone(self.db_manager.conn)
        self.assertIsNotNone(self.db_manager.cursor)

    def test_create_tables(self):
        # Check if the users table exists
        self.db_manager.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users';")
        self.assertIsNotNone(self.db_manager.cursor.fetchone())

    def test_execute_query(self):
        # Test inserting data
        result = self.db_manager.execute_query("INSERT INTO users (username, password, role) VALUES (?, ?, ?)", ("testuser", "testpass", "user"))
        self.assertTrue(result)

        # Test fetching data
        user = self.db_manager.fetch_one("SELECT * FROM users WHERE username = ?", ("testuser",))
        self.assertIsNotNone(user)
        self.assertEqual(user[1], "testuser")

    def test_fetch_all(self):
        self.db_manager.execute_query("INSERT INTO users (username, password, role) VALUES (?, ?, ?)", ("user1", "pass1", "user"))
        self.db_manager.execute_query("INSERT INTO users (username, password, role) VALUES (?, ?, ?)", ("user2", "pass2", "user"))
        users = self.db_manager.fetch_all("SELECT * FROM users")
        self.assertGreaterEqual(len(users), 2)

    def test_fetch_one(self):
        self.db_manager.execute_query("INSERT INTO users (username, password, role) VALUES (?, ?, ?)", ("singleuser", "singlepass", "user"))
        user = self.db_manager.fetch_one("SELECT * FROM users WHERE username = ?", ("singleuser",))
        self.assertIsNotNone(user)
        self.assertEqual(user[1], "singleuser")

    def test_duplicate_username_insertion(self):
        self.db_manager.execute_query("INSERT INTO users (username, password, role) VALUES (?, ?, ?)", ("duplicateuser", "pass", "user"))
        # Attempt to insert the same username again, should fail due to UNIQUE constraint
        result = self.db_manager.execute_query("INSERT INTO users (username, password, role) VALUES (?, ?, ?)", ("duplicateuser", "pass2", "user"))
        self.assertFalse(result)

if __name__ == '__main__':
    unittest.main()