import customtkinter
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class App(customtkinter.CTk):
    def __init__(self):
        super().__init__()

        self.title("WebLibya - نظام إدارة المخازن والمبيعات")
        self.geometry("1000x600")

        # configure grid layout (4x4)
        self.grid_columnconfigure(1, weight=1)
        self.grid_columnconfigure((2, 3), weight=0)
        self.grid_rowconfigure((0, 1, 2), weight=1)

        # create sidebar frame with widgets
        self.sidebar_frame = customtkinter.CTkFrame(self, width=140, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=4, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(4, weight=1)
        self.logo_label = customtkinter.CTkLabel(self.sidebar_frame, text="WebLibya", font=customtkinter.CTkFont(size=20, weight="bold"))
        self.logo_label.grid(row=0, column=0, padx=20, pady=20)

        # Add a placeholder for content area
        self.main_frame = customtkinter.CTkFrame(self, corner_radius=0)
        self.main_frame.grid(row=0, column=1, rowspan=4, columnspan=3, sticky="nsew")
        self.main_frame.grid_rowconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)

        from gui.login import LoginFrame
        self.login_screen = LoginFrame(self.main_frame, fg_color="transparent")
        self.login_screen.grid(row=0, column=0, sticky="nsew")


from core.auth import create_default_admin

if __name__ == "__main__":
    create_default_admin() # Call to ensure default admin exists
    app = App()
    app.mainloop()