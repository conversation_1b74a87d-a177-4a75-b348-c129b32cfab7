import unittest
import os
from unittest.mock import patch
from ..core.auth import Auth<PERSON>anager, create_default_admin
from ..db.db_manager import DBManager

class TestAuthManager(unittest.TestCase):
    def setUp(self):
        self.test_db_name = "test_weblibya_auth.db"
        # Ensure a clean database for each test
        if os.path.exists(self.test_db_name):
            os.remove(self.test_db_name)

        self.db_manager = DBManager(db_name=self.test_db_name)
        self.auth_manager = AuthManager(db_manager=self.db_manager)
        self.db_manager.connect()
        self.db_manager.create_tables()

    def tearDown(self):
        self.db_manager.disconnect()
        # Ensure the connection is closed before attempting to remove the file
        if self.db_manager.conn:
            self.db_manager.conn.close()
        if os.path.exists(self.test_db_name):
            os.remove(self.test_db_name)

    def test_authenticate_user_success(self):
        # Create a user for testing
        self.db_manager.execute_query(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ("testuser", "testpass", "admin")
        )
        self.assertTrue(self.auth_manager.authenticate_user("testuser", "testpass"))

    def test_authenticate_user_failure_wrong_password(self):
        self.db_manager.execute_query(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ("testuser", "testpass", "admin")
        )
        self.assertFalse(self.auth_manager.authenticate_user("testuser", "wrongpass"))

    def test_authenticate_user_failure_user_not_found(self):
        self.assertFalse(self.auth_manager.authenticate_user("nonexistent", "anypass"))

    @patch('builtins.input', side_effect=['admin_new', 'admin_new_pass'])
    def test_create_default_admin_new_user(self, mock_input):
        # Ensure no admin exists initially
        self.db_manager.execute_query("DELETE FROM users WHERE username = 'admin'")
        create_default_admin(self.db_manager)
        user = self.db_manager.fetch_one("SELECT * FROM users WHERE username = 'admin_new'")
        self.assertIsNotNone(user)
        self.assertEqual(user[2], 'admin_new_pass') # Check password
        self.assertEqual(user[3], 'admin') # Check role

    @patch('builtins.input', side_effect=['admin', 'admin_pass'])
    def test_create_default_admin_existing_user(self, mock_input):
        # Create an admin user first
        self.db_manager.execute_query(
            "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
            ("admin", "old_pass", "admin")
        )
        create_default_admin(self.db_manager)
        # Should not update existing admin password if user input is same
        user = self.db_manager.fetch_one("SELECT * FROM users WHERE username = 'admin'")
        self.assertIsNotNone(user)
        self.assertEqual(user[2], 'old_pass') # Password should remain old_pass

if __name__ == '__main__':
    unittest.main()